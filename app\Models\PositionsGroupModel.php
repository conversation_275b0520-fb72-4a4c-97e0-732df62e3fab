<?php

namespace App\Models;

use CodeIgniter\Model;

class PositionsGroupModel extends Model
{
    protected $table         = 'positions_groups';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';
    protected $protectFields = true;

    // Fields that are allowed to be set during insert/update operations.
    protected $allowedFields = [
        'org_id',
        'exercise_id',
        'parent_id',
        'group_name',
        'description',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Enable automatic handling of created_at and updated_at fields.
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Set the return type to array
    protected $returnType    = 'array';

    // Validation rules
    protected $validationRules = [
        'org_id' => 'required|numeric',
        'exercise_id' => 'required|numeric',
        'parent_id' => 'permit_empty|numeric',
        'group_name' => 'required|max_length[255]|is_unique[positions_groups.group_name,id,{id}]',
        'description' => 'permit_empty|max_length[500]'
    ];

    // Validation messages
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric' => 'Organization ID must be a number'
        ],
        'exercise_id' => [
            'required' => 'Exercise ID is required',
            'numeric' => 'Exercise ID must be a number'
        ],
        'parent_id' => [
            'numeric' => 'Parent ID must be a number'
        ],
        'group_name' => [
            'required' => 'Group name is required',
            'max_length' => 'Group name cannot exceed 255 characters',
            'is_unique' => 'A position group with this name already exists'
        ],
        'description' => [
            'max_length' => 'Description cannot exceed 500 characters'
        ]
    ];

    // Skip validation for these fields during updates
    protected $validationSkipOnUpdate = ['org_id', 'exercise_id'];
}